/**
 * This file is part of the CernVM File System.
 */



#define CVMFS_DUPLEX_FUSE_H_
#define __TEST_CVMFS_MOCKFUSE
#define CVMFS_USE_LIBFUSE 3
#define FUSE_USE_VERSION 31
#include <fuse3/fuse.h>
#include <fuse3/fuse_lowlevel.h>
#include <fuse3/fuse_opt.h>

#include <gtest/gtest.h>
#include <gmock/gmock.h>

#include "cvmfs.h"
#include "mountpoint.h"
#include "fuse_remount.h"
#include "fuse_inode_gen.h"
#include "catalog_mgr.h"
#include "catalog_mgr_client.h"
#include "glue_buffer.h"
#include "notification_client.h"
#include "monitor.h"
#include "options.h"
#include "quota_listener.h"
#include "quota_posix.h"
#include "talk.h"

using ::testing::_;
using ::testing::Return;
using ::testing::StrictMock;
using ::testing::NiceMock;
using ::testing::InSequence;

// Mock classes for testing cvmfs_release
class MockCatalogManager {
 public:
  MockCatalogManager() {}
  MOCK_CONST_METHOD1(MangleInode, fuse_ino_t(fuse_ino_t ino));
};

class MockPageCacheTracker {
 public:
  MockPageCacheTracker() {}
  MOCK_METHOD1(Close, void(uint64_t inode));
};

class MockCacheManager {
 public:
  MockCacheManager() {}
  MOCK_METHOD1(Close, int(int fd));
};

class MockMountPoint : public MountPoint {
 public:
  //MOCK_METHOD3(Create, testing::NiceMock<MockMountPoint>*(const std::string &fqrn,
  //                                 FileSystem *file_system,
  //                                 OptionsManager *options_mgr));
  MOCK_METHOD0(catalog_mgr, catalog::ClientCatalogManager*());
  MOCK_METHOD0(page_cache_tracker, glue::PageCacheTracker*());
  //MOCK_METHOD0(chunk_tables, ChunkTables*());
};



namespace cvmfs {
FileSystem *file_system_ = NULL;
//MountPoint *mount_point_ = NULL;
NiceMock<MockMountPoint>* mount_point_ = NULL;
TalkManager *talk_mgr_ = NULL;
NotificationClient *notification_client_ = NULL;
Watchdog *watchdog_ = NULL;
FuseRemounter *fuse_remounter_ = NULL;
InodeGenerationInfo inode_generation_info_;

}
static int Init(const loader::LoaderExports *loader_export) { return 0; }

#include "cvmfs.cc"


// Mock implementation of reply functions
int fuse_reply_err(fuse_req_t req, int err) {
    printf("[FUSE REPLY] Error: %d (%s)\n", err, strerror(err));
    return 0;
}



class T_Cvmfs : public ::testing::Test {
	 protected:
  virtual void SetUp() {
    // Set up FileSystem for use in FUSE callbacks
    // From t_mountpoint.cc, TODO(vvolkl): refactor
    repo_path_ = "repo";
    uuid_dummy_ = cvmfs::Uuid::Create("");
    used_fds_ = 1; //GetNoUsedFds();
    fd_cwd_ = open(".", O_RDONLY);
    ASSERT_GE(fd_cwd_, 0);
    tmp_path_ = CreateTempDir("./cvmfs_ut_cache");
    options_mgr_.SetValue("CVMFS_CACHE_BASE", tmp_path_);
    options_mgr_.SetValue("CVMFS_SHARED_CACHE", "no");
    options_mgr_.SetValue("CVMFS_MAX_RETRIES", "0");
    fs_info_.name = "unit-test";
    fs_info_.options_mgr = &options_mgr_;
    // Silence syslog error
    options_mgr_.SetValue("CVMFS_MOUNT_DIR", "/no/such/dir");


    //mock_mount_point_ = new NiceMock<MockMountPoint>();
    mock_catalog_mgr_ = new NiceMock<MockCatalogManager>();
    mock_page_cache_tracker_ = new NiceMock<MockPageCacheTracker>();
    mock_cache_mgr_ = new NiceMock<MockCacheManager>();

    // Set up default return values for mock objects
    //ON_CALL(*mock_mount_point_, catalog_mgr())
    //    .WillByDefault(Return(mock_catalog_mgr_));
    //ON_CALL(*mock_mount_point_, page_cache_tracker())
    //    .WillByDefault(Return(mock_page_cache_tracker_));
    //ON_CALL(*mock_mount_point_, chunk_tables())
    //    .WillByDefault(Return(reinterpret_cast<ChunkTables*>(mock_chunk_tables_)));
    //ON_CALL(*mock_file_system_, cache_mgr())
    //    .WillByDefault(Return(reinterpret_cast<CacheManager*>(mock_cache_mgr_)));
    //ON_CALL(*mock_file_system_, no_open_files())
     //   .WillByDefault(Return(mock_counter_));
    //ON_CALL(*mock_file_system_, hist_fs_release())
    //    .WillByDefault(Return(mock_statistics_));

    // MangleInode should be a noop as requested
    ON_CALL(*mock_catalog_mgr_, MangleInode(_))
        .WillByDefault([](fuse_ino_t ino) { return ino; });

  }

  virtual void TearDown() {
    delete uuid_dummy_;
    int retval = fchdir(fd_cwd_);
    ASSERT_EQ(0, retval);
    close(fd_cwd_);
    if (tmp_path_ != "")
      RemoveTree(tmp_path_);
    if (repo_path_ != "")
      RemoveTree(repo_path_);
  }

 protected:
  FileSystem::FileSystemInfo fs_info_;
  SimpleOptionsParser options_mgr_;
  string tmp_path_;
  string repo_path_;
  int fd_cwd_;
  unsigned used_fds_;
  /**
   * Initialize libuuid / open file descriptor on /dev/urandom
   */
  cvmfs::Uuid *uuid_dummy_;

  //NiceMock<MockFileSystem>* mock_file_system_;
  NiceMock<MockMountPoint>* mock_mount_point_;
  NiceMock<MockCatalogManager>* mock_catalog_mgr_;
  NiceMock<MockPageCacheTracker>* mock_page_cache_tracker_;
  //NiceMock<MockChunkTables>* mock_chunk_tables_;
  NiceMock<MockCacheManager>* mock_cache_mgr_;
};



TEST_F(T_Cvmfs, Basics) {
  EXPECT_EQ(0, 0);
  ASSERT_NE(g_cvmfs_exports, nullptr);
 cvmfs::file_system_=FileSystem::Create(fs_info_);

  fuse_ino_t ino = 100;
  fuse_req_t mock_req;
  fuse_file_info *fi;
  cvmfs::cvmfs_release_test(mock_req, ino, fi);
  delete cvmfs::file_system_;
}
